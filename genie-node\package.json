{"name": "genie", "version": "1.0.0", "description": "Powerful AI for Your Command Line - Node.js Implementation", "main": "src/main.js", "type": "module", "bin": {"genie": "./src/main.js"}, "scripts": {"start": "node src/main.js", "dev": "nodemon src/main.js", "setup": "node scripts/setup.js", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "test:watch": "node --experimental-vm-modules node_modules/jest/bin/jest.js --watch", "test:coverage": "node --experimental-vm-modules node_modules/jest/bin/jest.js --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "build": "echo 'No build step required for Node.js'", "clean": "rm -rf coverage/ .nyc_output/ node_modules/.cache/", "prepare": "chmod +x src/main.js"}, "keywords": ["ai", "cli", "tui", "assistant", "gemini", "terminal", "coding", "automation"], "author": "Genie Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/kcaldas/genie-node.git"}, "bugs": {"url": "https://github.com/kcaldas/genie-node/issues"}, "homepage": "https://github.com/kcaldas/genie-node#readme", "engines": {"node": ">=18.0.0"}, "dependencies": {"@google/generative-ai": "^0.21.0", "axios": "^1.7.7", "blessed": "^0.1.81", "chalk": "^5.3.0", "clipboardy": "^4.0.0", "commander": "^12.1.0", "diff": "^7.0.0", "dotenv": "^16.4.5", "glob": "^11.0.0", "handlebars": "^4.7.8", "inquirer": "^12.0.0", "js-yaml": "^4.1.0", "marked": "^14.1.2", "minimatch": "^10.0.1", "semver": "^7.6.3", "string-width": "^7.2.0", "strip-ansi": "^7.1.0", "uuid": "^10.0.0", "wrap-ansi": "^9.0.0", "ws": "^8.18.0"}, "devDependencies": {"@types/jest": "^29.5.13", "eslint": "^9.14.0", "jest": "^29.7.0", "nodemon": "^3.1.7", "prettier": "^3.3.3"}, "jest": {"testEnvironment": "node", "transform": {}, "collectCoverageFrom": ["src/**/*.js", "!src/**/*.test.js", "!src/main.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "eslintConfig": {"env": {"node": true, "es2022": true, "jest": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "rules": {"no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "no-console": "off"}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2}}