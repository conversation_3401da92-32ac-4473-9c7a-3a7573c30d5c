{"version": 3, "file": "takeUntil.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/takeUntil.ts"], "names": [], "mappings": ";;;AACA,qCAAuC;AACvC,2DAAgE;AAChE,qDAAoD;AACpD,qCAAoC;AAwCpC,SAAgB,SAAS,CAAI,QAA8B;IACzD,OAAO,cAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAChC,qBAAS,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,6CAAwB,CAAC,UAAU,EAAE,cAAM,OAAA,UAAU,CAAC,QAAQ,EAAE,EAArB,CAAqB,EAAE,WAAI,CAAC,CAAC,CAAC;QACvG,CAAC,UAAU,CAAC,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC;AACL,CAAC;AALD,8BAKC"}