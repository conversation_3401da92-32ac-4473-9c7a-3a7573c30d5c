<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- Allow Terminal/CLI execution -->
    <key>com.apple.security.automation.apple-events</key>
    <true/>
    
    <!-- Allow network access for AI APIs -->
    <key>com.apple.security.network.client</key>
    <true/>
    
    <!-- Allow file system access -->
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>
    
    <!-- Disable library validation for plugins -->
    <key>com.apple.security.cs.disable-library-validation</key>
    <true/>
    
    <!-- Allow unsigned executable memory (for any dynamic features) -->
    <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
    <true/>
</dict>
</plist>