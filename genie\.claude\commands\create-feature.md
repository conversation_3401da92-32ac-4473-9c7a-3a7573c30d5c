Please implement the following feature: $ARGUMENTS

## Instructions

1. **Feature analysis**:
   - Break down the feature requirements into specific tasks
   - Identify the files and components that need to be created/modified
   - Determine dependencies and integration points
   - Assess impact on existing functionality

2. **Design planning**:
   - Design the feature architecture and interfaces
   - Plan the data structures and APIs needed
   - Consider error handling and edge cases
   - Design for testability and maintainability

3. **Implementation workflow**:
   - Create or modify the core functionality
   - Follow existing project patterns and conventions
   - Implement proper error handling and validation
   - Add logging and debugging support where appropriate

4. **Testing strategy**:
   - Write unit tests for all new functions/methods
   - Create integration tests for feature workflows
   - Add edge case and error condition tests
   - Update existing tests if they're affected

5. **Documentation updates**:
   - Update relevant documentation files
   - Add code comments for complex logic
   - Update API documentation if applicable
   - Add usage examples where helpful

6. **Integration verification**:
   - Ensure the feature integrates properly with existing code
   - Test backwards compatibility
   - Verify no regressions in existing functionality
   - Check performance impact

7. **Quality assurance**:
   - Run all tests and ensure they pass
   - Check code formatting and linting
   - Verify type checking (if applicable)
   - Test the feature manually

8. **Finalization**:
   - Stage all changes with `git add`
   - Create a descriptive commit message
   - Suggest follow-up tasks or improvements

## Implementation Checklist

### Core Development
- [ ] Feature requirements analyzed and understood
- [ ] Architecture and design planned
- [ ] Core functionality implemented
- [ ] Error handling and validation added

### Testing
- [ ] Unit tests written and passing
- [ ] Integration tests created
- [ ] Edge cases covered
- [ ] Manual testing completed

### Documentation
- [ ] Code comments added where needed
- [ ] Documentation files updated
- [ ] Usage examples provided
- [ ] API docs updated (if applicable)

### Quality
- [ ] Code follows project conventions
- [ ] Linting and formatting passed
- [ ] No regressions introduced
- [ ] Performance impact assessed

## Success Confirmation

After implementing the feature, confirm:
- ✅ Feature fully implemented and working
- 🧪 Comprehensive tests written and passing
- 📚 Documentation updated appropriately
- 🔧 Code follows project standards
- 📝 Changes committed with clear message