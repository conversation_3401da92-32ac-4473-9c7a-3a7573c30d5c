{"version": 3, "file": "share.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/share.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,qDAAoD;AACpD,sCAAqC;AACrC,4CAA+C;AAG/C,qCAAuC;AAwIvC,SAAgB,KAAK,CAAI,OAA4B;IAA5B,wBAAA,EAAA,YAA4B;IAC3C,IAAA,KAAgH,OAAO,UAArF,EAAlC,SAAS,mBAAG,cAAM,OAAA,IAAI,iBAAO,EAAK,EAAhB,CAAgB,KAAA,EAAE,KAA4E,OAAO,aAAhE,EAAnB,YAAY,mBAAG,IAAI,KAAA,EAAE,KAAuD,OAAO,gBAAxC,EAAtB,eAAe,mBAAG,IAAI,KAAA,EAAE,KAA+B,OAAO,oBAAZ,EAA1B,mBAAmB,mBAAG,IAAI,KAAA,CAAa;IAUhI,OAAO,UAAC,aAAa;QACnB,IAAI,UAAyC,CAAC;QAC9C,IAAI,eAAyC,CAAC;QAC9C,IAAI,OAAmC,CAAC;QACxC,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,YAAY,GAAG,KAAK,CAAC;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,IAAM,WAAW,GAAG;YAClB,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,WAAW,EAAE,CAAC;YAC/B,eAAe,GAAG,SAAS,CAAC;QAC9B,CAAC,CAAC;QAGF,IAAM,KAAK,GAAG;YACZ,WAAW,EAAE,CAAC;YACd,UAAU,GAAG,OAAO,GAAG,SAAS,CAAC;YACjC,YAAY,GAAG,UAAU,GAAG,KAAK,CAAC;QACpC,CAAC,CAAC;QACF,IAAM,mBAAmB,GAAG;YAG1B,IAAM,IAAI,GAAG,UAAU,CAAC;YACxB,KAAK,EAAE,CAAC;YACR,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,EAAE,CAAC;QACtB,CAAC,CAAC;QAEF,OAAO,cAAO,CAAO,UAAC,MAAM,EAAE,UAAU;YACtC,QAAQ,EAAE,CAAC;YACX,IAAI,CAAC,UAAU,IAAI,CAAC,YAAY,EAAE;gBAChC,WAAW,EAAE,CAAC;aACf;YAMD,IAAM,IAAI,GAAG,CAAC,OAAO,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,SAAS,EAAE,CAAC,CAAC;YAOhD,UAAU,CAAC,GAAG,CAAC;gBACb,QAAQ,EAAE,CAAC;gBAKX,IAAI,QAAQ,KAAK,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,YAAY,EAAE;oBAClD,eAAe,GAAG,WAAW,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;iBACzE;YACH,CAAC,CAAC,CAAC;YAIH,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAE3B,IACE,CAAC,UAAU;gBAIX,QAAQ,GAAG,CAAC,EACZ;gBAMA,UAAU,GAAG,IAAI,2BAAc,CAAC;oBAC9B,IAAI,EAAE,UAAC,KAAK,IAAK,OAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAhB,CAAgB;oBACjC,KAAK,EAAE,UAAC,GAAG;wBACT,UAAU,GAAG,IAAI,CAAC;wBAClB,WAAW,EAAE,CAAC;wBACd,eAAe,GAAG,WAAW,CAAC,KAAK,EAAE,YAAY,EAAE,GAAG,CAAC,CAAC;wBACxD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAClB,CAAC;oBACD,QAAQ,EAAE;wBACR,YAAY,GAAG,IAAI,CAAC;wBACpB,WAAW,EAAE,CAAC;wBACd,eAAe,GAAG,WAAW,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;wBACtD,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,CAAC;iBACF,CAAC,CAAC;gBACH,qBAAS,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;aACzC;QACH,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC;AArGD,sBAqGC;AAED,SAAS,WAAW,CAClB,KAAiB,EACjB,EAAoD;IACpD,cAAU;SAAV,UAAU,EAAV,qBAAU,EAAV,IAAU;QAAV,6BAAU;;IAEV,IAAI,EAAE,KAAK,IAAI,EAAE;QACf,KAAK,EAAE,CAAC;QACR,OAAO;KACR;IAED,IAAI,EAAE,KAAK,KAAK,EAAE;QAChB,OAAO;KACR;IAED,IAAM,YAAY,GAAG,IAAI,2BAAc,CAAC;QACtC,IAAI,EAAE;YACJ,YAAY,CAAC,WAAW,EAAE,CAAC;YAC3B,KAAK,EAAE,CAAC;QACV,CAAC;KACF,CAAC,CAAC;IAEH,OAAO,qBAAS,CAAC,EAAE,wCAAI,IAAI,IAAE,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;AACxD,CAAC"}