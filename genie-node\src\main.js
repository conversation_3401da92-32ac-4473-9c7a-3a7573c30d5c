#!/usr/bin/env node

/**
 * Genie - Powerful AI for Your Command Line
 * Main entry point that routes to CLI or TUI based on arguments
 */

// Import CLI modules
import { RootCommand } from './cli/root.js';
import { getVersion } from './utils/version.js';

/**
 * Main function that determines whether to run CLI or TUI mode
 */
async function main() {
  try {
    // Set custom version template that shows more detailed version info
    const versionInfo = getVersion();

    // Check if we have command line arguments (excluding node and script name)
    const args = process.argv.slice(2);

    if (args.length > 0) {
      // Has arguments → CLI mode
      const rootCommand = new RootCommand();
      rootCommand.version(versionInfo.toString());
      await rootCommand.parseAsync(process.argv);
    } else {
      // No arguments → TUI mode
      try {
        const { createTUI } = await import('./tui/index.js');
        const tui = await createTUI();
        await tui.start();
      } catch (error) {
        console.error('TUI mode failed:', error.message);
        console.log('Try using CLI mode: genie ask "your question"');
        process.exit(1);
      }
    }
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run main function
main().catch((error) => {
  console.error('Fatal error:', error);
  process.exit(1);
});
