name: 💡 Feature Request
description: Suggest a new feature or enhancement
title: "[Feature] "
labels: ["enhancement", "triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for suggesting a new feature! Please help us understand your idea better.

  - type: textarea
    id: problem
    attributes:
      label: Problem Statement
      description: What problem does this feature solve? Is your feature request related to a problem?
      placeholder: I'm always frustrated when...
    validations:
      required: true

  - type: textarea
    id: solution
    attributes:
      label: Proposed Solution
      description: Describe the feature you'd like to see
      placeholder: I would like <PERSON><PERSON> to...
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: Alternatives Considered
      description: Have you considered any alternative solutions or workarounds?

  - type: dropdown
    id: category
    attributes:
      label: Feature Category
      description: Which area does this feature relate to?
      options:
        - CLI interface
        - TUI interface
        - AI/LLM integration
        - Tools system
        - Configuration
        - Documentation
        - Performance
        - Other
    validations:
      required: true

  - type: checkboxes
    id: impact
    attributes:
      label: Impact
      description: How would this feature help?
      options:
        - label: Improves user experience
        - label: Adds new functionality
        - label: Improves performance
        - label: Enhances developer workflow
        - label: Better integration with existing tools

  - type: textarea
    id: examples
    attributes:
      label: Usage Examples
      description: Provide examples of how this feature would be used
      placeholder: |
        ```bash
        genie new-feature "example usage"
        ```

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: Add any other context, mockups, or references about the feature