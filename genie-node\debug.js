#!/usr/bin/env node

/**
 * Debug script to test basic functionality
 */

try {
  console.log('Starting debug...');
  
  // Test basic imports
  console.log('Testing imports...');
  
  import('./src/utils/version.js').then(({ getVersion }) => {
    console.log('Version module loaded');
    const version = getVersion();
    console.log('Version:', version.toString());
    
    // Test CLI import
    return import('./src/cli/root.js');
  }).then(({ RootCommand }) => {
    console.log('CLI module loaded');
    const rootCommand = new RootCommand();
    console.log('Root command created');
    
    // Test help
    rootCommand.outputHelp();
    
  }).catch(error => {
    console.error('Error:', error);
    process.exit(1);
  });
  
} catch (error) {
  console.error('Sync error:', error);
  process.exit(1);
}
