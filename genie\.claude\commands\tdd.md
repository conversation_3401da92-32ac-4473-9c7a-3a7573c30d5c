# Implement a feature using Test-Driven Development (TDD)

# TDD Workflow

1. Write a failing test that describes the desired feature.
2. Write the minimum code necessary to make the test pass.
3. Refactor/Generalize the code while ensuring all tests still pass (if necessary).
4. Commit the changes with a clear message.

# TDD Rules

- Always start with a failing test.
- Write only the code necessary to pass the test.
- Commit only when all tests are passing.
- Never delete tests without explicit permission.
- Commits will be separated into commits that change the behavior of the code and commits that only change the structure of the code.
- Before modifying code we consider whether tidying first would make the change easier.
- We are writing the code like <PERSON> Beck would write it: Write the code one test at a time. Write the test. Get it to compile. Get it to pass. Tidy after if appropriate.
- Don't add comments like "This test will fail" in test code.

# How to Start

Create a TODO list before jumping
into code. If during implementation you notice a test is needed that is
not in the list, add it at the appropriate place in the list. As you
complete tests, cross them off the list.

# What to Implement

$ARGUMENTS
