/**
 * Root CLI command for Genie
 */

import { Command } from 'commander';
import { getVersion } from '../utils/version.js';
import { setGlobalLogger, newDefaultLogger, newVerboseLogger, newQuietLogger } from '../logging/logger.js';
import { AskCommand } from './ask.js';
import { hasStdinInput, readStdinInput } from './stdin.js';

/**
 * Root command class
 */
export class RootCommand extends Command {
  constructor() {
    super();
    
    this.name('genie')
      .description('Genie AI coding assistant');

    // Global flags
    this.option('--cwd <directory>', 'working directory for Genie operations')
      .option('--persona <name>', 'persona to use (e.g., engineer, product_owner, persona_creator)')
      .option('-v, --verbose', 'verbose output (debug level)')
      .option('-q, --quiet', 'quiet output (errors only)');

    // Set up hooks
    this.hook('preAction', this.preAction.bind(this));

    // Add subcommands
    this.addCommand(new AskCommand());
  }

  /**
   * Pre-action hook to set up logging and initialize Genie
   * @param {Command} thisCommand - The command being executed
   * @param {Command} actionCommand - The action command
   */
  async preAction(thisCommand, actionCommand) {
    const options = thisCommand.opts();

    // Configure logger based on flags
    let logger;
    if (options.quiet) {
      logger = newQuietLogger();
    } else if (options.verbose) {
      logger = newVerboseLogger();
    } else {
      logger = newDefaultLogger();
    }
    setGlobalLogger(logger);

    // Store options for subcommands to access
    this.globalOptions = {
      workingDir: options.cwd,
      persona: options.persona,
      verbose: options.verbose,
      quiet: options.quiet,
    };
  }

  /**
   * Default action when no subcommand is provided (TUI mode)
   * @param {Object} options - Command options
   */
  async defaultAction(options) {
    // Check for stdin input before starting TUI
    let stdinContent = '';
    if (hasStdinInput()) {
      stdinContent = await readStdinInput();
    }

    // Import TUI dynamically to avoid loading it for CLI commands
    const { createTUI } = await import('../tui/index.js');

    // Create TUI with global options (use options directly if globalOptions not set)
    const globalOpts = this.globalOptions || {};
    const tui = await createTUI({
      workingDir: globalOpts.workingDir || options.cwd,
      persona: globalOpts.persona || options.persona,
    });

    // Start the TUI with the initial message if provided
    await tui.startWithMessage(stdinContent);
  }

  /**
   * Get global options set by preAction
   * @returns {Object} Global options
   */
  getGlobalOptions() {
    return this.globalOptions || {};
  }
}
