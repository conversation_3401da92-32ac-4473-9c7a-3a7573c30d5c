/**
 * Ask command for CLI
 */

import { Command } from 'commander';
import { hasStdinInput, readStdinInput } from './stdin.js';

/**
 * Ask command class
 */
export class AskCommand extends Command {
  constructor() {
    super();

    this.name('ask')
      .description('Ask <PERSON>ie a question')
      .argument('[message]', 'message to send to <PERSON><PERSON>')
      .action(async (message, options) => {
        await this.executeAsk(message, options);
      });
  }

  /**
   * Execute the ask command
   * @param {string} message - Message argument
   * @param {Object} options - Command options
   */
  async executeAsk(message, options) {
    try {
      // Get global options from parent command
      const globalOptions = this.parent?.getGlobalOptions() || {};

      // Determine the message to send
      let finalMessage = message;

      // If no message provided as argument, check stdin
      if (!finalMessage) {
        if (hasStdinInput()) {
          finalMessage = await readStdinInput();
        } else {
          console.error('Error: No message provided. Use: genie ask "your message" or pipe input');
          process.exit(1);
        }
      }

      // Ensure finalMessage is a string
      if (typeof finalMessage !== 'string') {
        console.error('Error: Invalid message type');
        process.exit(1);
      }

      if (!finalMessage.trim()) {
        console.error('Error: Empty message provided');
        process.exit(1);
      }

      // Create and initialize Genie
      const genie = await this.createGenie();
      const session = await genie.start(globalOptions.workingDir, globalOptions.persona);

      // Set up event listeners for response
      const eventBus = genie.getEventBus();
      
      // Wait for response
      const response = await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Request timed out after 60 seconds'));
        }, 60000);

        eventBus.subscribe('chat.response', (event) => {
          clearTimeout(timeout);
          if (event.error) {
            reject(event.error);
          } else {
            resolve(event.response);
          }
        });

        // Send the message
        genie.chat({}, finalMessage).catch(reject);
      });

      // Output the response
      console.log(response);

    } catch (error) {
      console.error('Error:', error.message);
      process.exit(1);
    }
  }

  /**
   * Create and configure Genie instance
   * @returns {Promise<import('../core/genie.js').Genie>} Configured Genie instance
   */
  async createGenie() {
    const { createGenieForCLI } = await import('../core/factory.js');
    return createGenieForCLI();
  }
}
