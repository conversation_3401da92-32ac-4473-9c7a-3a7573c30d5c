# Code of Conduct

## Our Pledge

We pledge to make participation in Genie a harassment-free experience for everyone, regardless of age, body size, disability, ethnicity, gender identity and expression, level of experience, nationality, personal appearance, race, religion, or sexual identity and orientation.

## Our Standards

### Positive behavior includes:
- Being respectful and inclusive in language and actions
- Focusing on what is best for the community
- Showing empathy towards other community members
- Being open to constructive feedback
- Gracefully accepting responsibility and apologizing for mistakes

### Unacceptable behavior includes:
- Harassment, trolling, or discriminatory language
- Personal attacks or political arguments
- Publishing private information without consent
- Spam, off-topic posts, or excessive self-promotion
- Any conduct that would be inappropriate in a professional setting

## Scope

This Code of Conduct applies to all Genie community spaces, including:
- GitHub repository (issues, PRs, discussions)
- Documentation and code comments
- Community forums and chat channels
- Events and meetups (virtual or in-person)

## Enforcement

Community leaders are responsible for clarifying and enforcing standards. They have the right to remove, edit, or reject comments, commits, code, issues, and other contributions that don't align with this Code of Conduct.

### Reporting
Report unacceptable behavior to the project maintainers at [maintainer email]. All reports will be handled confidentially.

### Consequences
Violations may result in:
1. **Warning** - Private message explaining the violation
2. **Temporary ban** - Temporary restriction from community spaces
3. **Permanent ban** - Permanent removal from all community spaces

## Attribution

This Code of Conduct is adapted from the [Contributor Covenant](https://www.contributor-covenant.org/), version 2.1.