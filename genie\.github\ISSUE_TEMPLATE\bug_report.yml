name: 🐛 Bug Report
description: Report a bug or unexpected behavior
title: "[Bug] "
labels: ["bug", "triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to report a bug! Please fill out this form to help us understand and fix the issue.

  - type: textarea
    id: description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is.
      placeholder: Describe what happened and what you expected to happen
    validations:
      required: true

  - type: textarea
    id: reproduction
    attributes:
      label: Steps to Reproduce
      description: Provide detailed steps to reproduce the behavior
      placeholder: |
        1. Run command '...'
        2. Type input '...'
        3. See error
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior
      description: What should have happened instead?
    validations:
      required: true

  - type: textarea
    id: environment
    attributes:
      label: Environment
      description: Please provide information about your environment
      value: |
        - OS: [e.g. macOS 14.1, Ubuntu 22.04, Windows 11]
        - Genie version: [e.g. v0.1.0]
        - Installation method: [binary, Docker, source]
        - Terminal: [e.g. iTerm2, Windows Terminal, GNOME Terminal]
        - Shell: [e.g. zsh, bash, fish]
    validations:
      required: true

  - type: textarea
    id: logs
    attributes:
      label: Logs/Output
      description: Include any relevant logs, error messages, or output
      render: shell

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: Add any other context, screenshots, or information about the problem