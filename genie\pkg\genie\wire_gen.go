// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package genie

import (
	"github.com/kcaldas/genie/pkg/ai"
	"github.com/kcaldas/genie/pkg/config"
	"github.com/kcaldas/genie/pkg/ctx"
	"github.com/kcaldas/genie/pkg/events"
	"github.com/kcaldas/genie/pkg/llm/genai"
	"github.com/kcaldas/genie/pkg/mcp"
	"github.com/kcaldas/genie/pkg/persona"
	"github.com/kcaldas/genie/pkg/prompts"
	"github.com/kcaldas/genie/pkg/tools"
)

// Injectors from wire.go:

// ProvideToolRegistry provides a tool registry with interactive tools and MCP tools
func ProvideToolRegistry() (tools.Registry, error) {
	eventsEventBus := ProvideEventBus()
	todoManager := ProvideTodoManager()
	mcpClient, err := ProvideMCPClient()
	if err != nil {
		return nil, err
	}
	registry := tools.NewRegistryWithMCP(eventsEventBus, todoManager, mcpClient)
	return registry, nil
}

// ProvideOutputFormatter provides a tool output formatter
func ProvideOutputFormatter() (tools.OutputFormatter, error) {
	registry, err := ProvideToolRegistry()
	if err != nil {
		return nil, err
	}
	outputFormatter := tools.NewOutputFormatter(registry)
	return outputFormatter, nil
}

func ProvideProjectCtxManager() ctx.ProjectContextPartProvider {
	subscriber := ProvideSubscriber()
	projectContextPartProvider := ctx.NewProjectCtxManager(subscriber)
	return projectContextPartProvider
}

func ProvideChatCtxManager() ctx.ChatContextPartProvider {
	eventsEventBus := ProvideEventBus()
	chatContextPartProvider := ctx.NewChatCtxManager(eventsEventBus)
	return chatContextPartProvider
}

func ProvideFileContextPartsProvider() *ctx.FileContextPartsProvider {
	eventsEventBus := ProvideEventBus()
	fileContextPartsProvider := ctx.NewFileContextPartsProvider(eventsEventBus)
	return fileContextPartsProvider
}

func ProvideTodoContextPartsProvider() *ctx.TodoContextPartProvider {
	eventsEventBus := ProvideEventBus()
	todoContextPartProvider := ctx.NewTodoContextPartProvider(eventsEventBus)
	return todoContextPartProvider
}

func ProvideContextManager() ctx.ContextManager {
	contextPartProviderRegistry := ProvideContextRegistry()
	contextManager := ctx.NewContextManager(contextPartProviderRegistry)
	return contextManager
}

func ProvideSessionManager() SessionManager {
	publisher := ProvidePublisher()
	sessionManager := NewSessionManager(publisher)
	return sessionManager
}

// ProvideGen is an injector function - Wire will generate the implementation
func ProvideGen() (ai.Gen, error) {
	manager := ProvideConfigManager()
	gen, err := ProvideAIGenWithCapture(manager)
	if err != nil {
		return nil, err
	}
	return gen, nil
}

// ProvidePromptLoader is an injector function - Wire will generate the implementation
func ProvidePromptLoader() (prompts.Loader, error) {
	publisher := ProvidePublisher()
	registry, err := ProvideToolRegistry()
	if err != nil {
		return nil, err
	}
	loader := prompts.NewPromptLoader(publisher, registry)
	return loader, nil
}

// ProvidePersonaPromptFactory provides the persona-aware prompt factory
func ProvidePersonaPromptFactory() (persona.PersonaAwarePromptFactory, error) {
	loader, err := ProvidePromptLoader()
	if err != nil {
		return nil, err
	}
	personaAwarePromptFactory := persona.NewPersonaPromptFactory(loader)
	return personaAwarePromptFactory, nil
}

// ProviderPromptRunner provides the prompt runner
func ProvidePromptRunner() (PromptRunner, error) {
	gen, err := ProvideGen()
	if err != nil {
		return nil, err
	}
	bool2 := _wireBoolValue
	promptRunner := NewDefaultPromptRunner(gen, bool2)
	return promptRunner, nil
}

var (
	_wireBoolValue = false
)

// ProvidePersonaManager provides the persona manager
func ProvidePersonaManager() (persona.PersonaManager, error) {
	personaAwarePromptFactory, err := ProvidePersonaPromptFactory()
	if err != nil {
		return nil, err
	}
	manager := ProvideConfigManager()
	personaManager := persona.NewDefaultPersonaManager(personaAwarePromptFactory, manager)
	return personaManager, nil
}

// ProvideGenie provides a complete Genie instance using Wire
func ProvideGenie() (Genie, error) {
	promptRunner, err := ProvidePromptRunner()
	if err != nil {
		return nil, err
	}
	sessionManager := ProvideSessionManager()
	contextManager := ProvideContextManager()
	eventsEventBus := ProvideEventBus()
	outputFormatter, err := ProvideOutputFormatter()
	if err != nil {
		return nil, err
	}
	personaManager, err := ProvidePersonaManager()
	if err != nil {
		return nil, err
	}
	manager := ProvideConfigManager()
	genie := NewGenie(promptRunner, sessionManager, contextManager, eventsEventBus, outputFormatter, personaManager, manager)
	return genie, nil
}

// wire.go:

// Shared event bus instance
var eventBus = events.NewEventBus()

func ProvideEventBus() events.EventBus {
	return eventBus
}

func ProvidePublisher() events.Publisher {
	return eventBus
}

func ProvideSubscriber() events.Subscriber {
	return eventBus
}

// ProvideTodoManager provides a shared todo manager instance
func ProvideTodoManager() tools.TodoManager {
	return tools.NewTodoManager()
}

// ProvideMCPClient provides an MCP client
func ProvideMCPClient() (tools.MCPClient, error) {
	client, err := mcp.NewMCPClientFromConfig()
	if err != nil {
		return nil, err
	}
	return client, nil
}

func ProvideContextRegistry() *ctx.ContextPartProviderRegistry {

	registry := ctx.NewContextPartProviderRegistry()

	projectManager := ProvideProjectCtxManager()
	chatManager := ProvideChatCtxManager()
	fileProvider := ProvideFileContextPartsProvider()
	todoProvider := ProvideTodoContextPartsProvider()

	registry.Register(projectManager)
	registry.Register(chatManager)
	registry.Register(fileProvider)
	registry.Register(todoProvider)

	return registry
}

// ProvideAIGenWithCapture creates the AI Gen with optional capture middleware
func ProvideAIGenWithCapture(configManager config.Manager) (ai.Gen, error) {

	baseGen, err := genai.NewClient(eventBus)
	if err != nil {
		return nil, err
	}

	captureConfig := ai.GetCaptureConfigFromEnv("genai")

	if captureConfig.Enabled {
		baseGen = ai.NewCaptureMiddleware(baseGen, captureConfig)
	}

	retryConfig := ai.GetRetryConfigFromEnv(configManager)

	if retryConfig.Enabled {
		return ai.NewRetryMiddleware(baseGen, retryConfig), nil
	}

	return baseGen, nil
}

// ProvideConfigManager provides a configuration manager
func ProvideConfigManager() config.Manager {
	return config.NewConfigManager()
}
