package controllers

import (
	"fmt"
	"time"

	"github.com/awesome-gocui/gocui"
	"github.com/kcaldas/genie/cmd/events"
	"github.com/kcaldas/genie/cmd/tui/component"
	"github.com/kcaldas/genie/cmd/tui/helpers"
	"github.com/kcaldas/genie/cmd/tui/layout"
	"github.com/kcaldas/genie/cmd/tui/presentation"
	"github.com/kcaldas/genie/cmd/tui/types"
	core_events "github.com/kcaldas/genie/pkg/events"
	"github.com/kcaldas/genie/pkg/logging"
)

type ToolConfirmationController struct {
	*ConfirmationKeyHandler
	gui                   types.Gui
	stateAccessor         types.IStateAccessor
	layoutManager         *layout.LayoutManager
	inputComponent        types.Component
	configManager         *helpers.ConfigManager
	ConfirmationComponent *component.ConfirmationComponent
	textViewerComponent   *component.TextViewerComponent
	eventBus              core_events.EventBus
	commandEventBus       *events.CommandEventBus
}

func NewToolConfirmationController(
	gui types.Gui,
	stateAccessor types.IStateAccessor,
	layoutManager *layout.LayoutManager,
	inputComponent types.Component,
	textViewerComponent *component.TextViewerComponent,
	configManager *helpers.ConfigManager,
	eventBus core_events.EventBus,
	commandEventBus *events.CommandEventBus,
) *ToolConfirmationController {
	c := ToolConfirmationController{
		ConfirmationKeyHandler: NewConfirmationKeyHandler(),
		gui:                    gui,
		stateAccessor:          stateAccessor,
		layoutManager:          layoutManager,
		inputComponent:         inputComponent,
		textViewerComponent:    textViewerComponent,
		configManager:          configManager,
		eventBus:               eventBus,
		commandEventBus:        commandEventBus,
	}

	eventBus.Subscribe("tool.confirmation.request", func(e interface{}) {
		if event, ok := e.(core_events.ToolConfirmationRequest); ok {
			logging.GetGlobalLogger().Debug(fmt.Sprintf("Event consumed: %s", event.Topic()))
			c.HandleToolConfirmationRequest(event)
		}
	})

	// Subscribe to user cancel input
	commandEventBus.Subscribe("user.input.cancel", func(event interface{}) {
		c.stateAccessor.SetWaitingConfirmation(false)
		// Hide the right panel
		c.layoutManager.HideRightPanel()
		c.layoutManager.SwapComponent("input", c.inputComponent)
		// Re-render to update the view
		c.gui.GetGui().Update(func(g *gocui.Gui) error {
			if err := c.inputComponent.Render(); err != nil {
				return err
			}
			// Focus back on input
			return c.focusPanelByName("input")
		})
	})

	return &c
}

// logger returns the current global logger (updated dynamically when debug is toggled)
func (tc *ToolConfirmationController) logger() logging.Logger {
	return logging.GetGlobalLogger()
}

func (tc *ToolConfirmationController) HandleToolConfirmationRequest(event core_events.ToolConfirmationRequest) error {
	// Check if tool has auto-accept enabled
	config := tc.configManager.GetConfig()
	if toolConfig, exists := config.ToolConfigs[event.ToolName]; exists && toolConfig.AutoAccept {
		// Auto-accept without showing dialog
		tc.logger().Debug(fmt.Sprintf("Auto-accepting confirmation for tool: %s", event.ToolName))
		tc.eventBus.Publish("tool.confirmation.response", core_events.ToolConfirmationResponse{
			ExecutionID: event.ExecutionID,
			Confirmed:   true,
		})
		return nil
	}

	// Set confirmation state
	tc.stateAccessor.SetWaitingConfirmation(true)

	// Always create a new confirmation component for tool confirmations
	tc.ConfirmationComponent = component.NewConfirmationComponent(
		tc.gui,
		tc.configManager,
		event.ExecutionID,
		"1 - Yes | 2 - No",
		tc.HandleToolConfirmationResponse, // Connect to controller's response handler
	)

	// Swap to confirmation component
	tc.layoutManager.SwapComponent("input", tc.ConfirmationComponent)

	// Apply secondary theme color to border and title after swap
	tc.gui.GetGui().Update(func(g *gocui.Gui) error {
		if view, err := g.View("input"); err == nil {
			// Get secondary color from theme
			theme := tc.configManager.GetTheme()
			if theme != nil {
				// Convert secondary color to gocui color for border and title
				secondaryColor := presentation.ConvertAnsiToGocuiColor(theme.Secondary)
				view.FrameColor = secondaryColor
				view.TitleColor = secondaryColor
			}
		}
		return nil
	})

	tc.gui.PostUIUpdate(func() {
		// Render messages first
		if err := tc.ConfirmationComponent.Render(); err != nil {
			// TODO handle error
		}
	})

	// Focus the confirmation component (same view name as input)
	// Show the confirmation message in the text-viewer panel
	tc.logger().Debug("Showing confirmation message in viewer", "message", event.Message, "tool", event.ToolName)
	tc.showMessageInViewer(event.Message, fmt.Sprintf("Tool: %s", event.ToolName))

	return tc.focusPanelByName("input")
}

func (tc *ToolConfirmationController) showMessageInViewer(message, title string) {
	// Show the right panel with text-viewer
	tc.layoutManager.ShowRightPanel("text-viewer")

	// Set content using the text viewer component
	tc.textViewerComponent.SetContentWithType(message, "text")
	tc.textViewerComponent.SetTitle(title)
	
	// Add delay to avoid race conditions (like user confirmation controller)
	time.Sleep(50 * time.Millisecond)
	
	// Use a separate GUI update for rendering to avoid race conditions
	tc.gui.PostUIUpdate(func() {
		// Ensure the view exists before rendering
		if view, err := tc.gui.GetGui().View("text-viewer"); err == nil && view != nil {
			tc.textViewerComponent.Render()
		}
	})
}

// HandleKeyPress processes a key press and determines if it's a confirmation response
func (tc *ToolConfirmationController) HandleKeyPress(key interface{}) (bool, error) {
	// Check if we have an active confirmation
	if tc.ConfirmationComponent == nil {
		return false, nil
	}

	// Use the embedded key handler to interpret the key
	confirmed, handled := tc.InterpretKey(key)
	if handled {
		executionID := tc.ConfirmationComponent.ExecutionID
		return true, tc.HandleToolConfirmationResponse(executionID, confirmed)
	}

	return false, nil
}

func (tc *ToolConfirmationController) HandleToolConfirmationResponse(executionID string, confirmed bool) error {
	// Clear confirmation state
	tc.stateAccessor.SetWaitingConfirmation(false)

	// Publish confirmation response
	tc.logger().Debug(fmt.Sprintf("Event published: tool.confirmation.response (confirmed=%v)", confirmed))
	tc.eventBus.Publish("tool.confirmation.response", core_events.ToolConfirmationResponse{
		ExecutionID: executionID,
		Confirmed:   confirmed,
	})

	// Hide the right panel
	tc.layoutManager.HideRightPanel()

	// Swap back to input component
	tc.layoutManager.SwapComponent("input", tc.inputComponent)

	// Re-render to update the view
	tc.gui.GetGui().Update(func(g *gocui.Gui) error {
		if err := tc.inputComponent.Render(); err != nil {
			return err
		}
		// Focus back on input
		return tc.focusPanelByName("input")
	})

	return nil
}

func (tc *ToolConfirmationController) focusPanelByName(panelName string) error {
	// Delegate to layout manager for panel focusing
	if err := tc.layoutManager.FocusPanel(panelName); err != nil {
		return err
	}
	return nil
}
