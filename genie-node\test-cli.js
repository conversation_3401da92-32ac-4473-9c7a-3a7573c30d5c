#!/usr/bin/env node

/**
 * Simple CLI test without API key requirement
 */

import { createMinimalGenie } from './src/core/factory.js';

async function testCLI() {
  try {
    console.log('🧞 Testing Genie CLI functionality...\n');

    // Create minimal Genie instance (uses mock AI)
    const genie = await createMinimalGenie();
    console.log('✅ Genie instance created');

    // Start Genie
    const session = await genie.start(process.cwd(), 'genie');
    console.log('✅ Genie started');
    console.log(`   Session ID: ${session.getId()}`);
    console.log(`   Working Directory: ${session.getWorkingDirectory()}`);
    console.log(`   Persona: ${session.getPersona().getName()}`);

    // Test status
    const status = genie.getStatus();
    console.log('✅ Status retrieved');
    console.log(`   Connected: ${status.connected}`);
    console.log(`   Backend: ${status.backend}`);

    // Test chat (async)
    console.log('\n🗣️  Testing chat...');
    
    const eventBus = genie.getEventBus();
    
    // Set up event listeners
    eventBus.subscribe('chat.started', (event) => {
      console.log(`   Started: ${event.message}`);
    });

    eventBus.subscribe('chat.response', (event) => {
      if (event.error) {
        console.error(`   ❌ Error: ${event.error.message}`);
      } else {
        console.log(`   ✅ Response: ${event.response}`);
      }
      console.log('\n🎉 CLI test completed successfully!');
      process.exit(0);
    });

    // Send test message
    await genie.chat({}, 'Hello, this is a test message!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run test
testCLI();
