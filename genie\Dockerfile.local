# Multi-stage build for local development
FROM golang:1.23-alpine AS builder

# Set working directory
WORKDIR /app

# Install git (needed for go modules)
RUN apk add --no-cache git

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the binary
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o genie ./cmd/genie

# Final stage - minimal runtime image
FROM alpine:3.20

# Install ca-certificates for HTTPS requests and git for potential operations
RUN apk --no-cache add ca-certificates git

# Create non-root user for security
RUN addgroup -g 1001 -S genie && \
    adduser -u 1001 -S genie -G genie

# Set working directory
WORKDIR /home/<USER>

# Copy binary from builder stage
COPY --from=builder /app/genie /usr/local/bin/genie

# Create directories for genie configuration and history
RUN mkdir -p /home/<USER>/.genie && \
    chown -R genie:genie /home/<USER>

# Switch to non-root user
USER genie

# Create a volume for persistent data (optional)
VOLUME ["/home/<USER>/.genie"]

# Set default command
ENTRYPOINT ["genie"]
CMD ["--help"]

# Labels for better maintainability
LABEL org.opencontainers.image.title="Genie" \
      org.opencontainers.image.description="AI-powered coding assistant with CLI and TUI interfaces" \
      org.opencontainers.image.vendor="kcaldas" \
      org.opencontainers.image.licenses="MIT" \
      org.opencontainers.image.source="https://github.com/kcaldas/genie"